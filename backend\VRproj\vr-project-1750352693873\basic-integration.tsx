import React, { useState } from 'react';
import { WebVRGame, WebVRGameConfig } from '../src';

/**
 * Basic integration example showing how to use the WebVR Game component
 * in a React application with custom configuration and event handling.
 */
export default function BasicIntegration() {
  const [score, setScore] = useState(0);
  const [gameStats, setGameStats] = useState({
    hits: 0,
    shots: 0,
    isVRActive: false,
  });

  const gameConfig: WebVRGameConfig = {
    // Custom game settings
    game: {
      targetCount: 4,
      scorePerHit: 25,
      bulletSpeed: 12,
      bulletTimeToLive: 3,
      targetRespawnTime: 800,
    },
    
    // Visual customization
    visual: {
      backgroundColor: 0x001122,
      environment: 'sunset',
      cameraFov: 80,
      scoreColor: 0x00ff88,
      scoreFontSize: 0.6,
    },
    
    // UI customization
    ui: {
      enterVRButtonText: '🥽 Enter VR Game',
      buttonStyle: {
        position: 'fixed',
        bottom: '30px',
        left: '50%',
        transform: 'translateX(-50%)',
        fontSize: '18px',
        padding: '12px 24px',
        backgroundColor: '#0066cc',
        color: 'white',
        border: 'none',
        borderRadius: '8px',
        cursor: 'pointer',
      },
    },
    
    // Event handlers
    onScoreChange: (newScore) => {
      setScore(newScore);
      console.log('Score updated:', newScore);
    },
    
    onTargetHit: (targetIndex, points) => {
      setGameStats(prev => ({
        ...prev,
        hits: prev.hits + 1,
      }));
      console.log(`🎯 Hit target ${targetIndex} for ${points} points!`);
    },
    
    onBulletFired: () => {
      setGameStats(prev => ({
        ...prev,
        shots: prev.shots + 1,
      }));
      console.log('💥 Bullet fired!');
    },
    
    onVREnter: () => {
      setGameStats(prev => ({ ...prev, isVRActive: true }));
      console.log('🥽 Entered VR mode');
    },
    
    onVRExit: () => {
      setGameStats(prev => ({ ...prev, isVRActive: false }));
      console.log('👋 Exited VR mode');
    },
  };

  const accuracy = gameStats.shots > 0 
    ? ((gameStats.hits / gameStats.shots) * 100).toFixed(1) 
    : '0';

  const resetStats = () => {
    setScore(0);
    setGameStats({
      hits: 0,
      shots: 0,
      isVRActive: gameStats.isVRActive,
    });
  };

  return (
    <div style={{ width: '100vw', height: '100vh', position: 'relative' }}>
      {/* Game Stats Overlay */}
      <div style={{
        position: 'absolute',
        top: '20px',
        left: '20px',
        zIndex: 1000,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        padding: '15px',
        borderRadius: '8px',
        fontFamily: 'monospace',
        fontSize: '14px',
        minWidth: '200px',
      }}>
        <h3 style={{ margin: '0 0 10px 0', color: '#00ff88' }}>
          Game Stats
        </h3>
        <div>Score: <strong>{score}</strong></div>
        <div>Hits: <strong>{gameStats.hits}</strong></div>
        <div>Shots: <strong>{gameStats.shots}</strong></div>
        <div>Accuracy: <strong>{accuracy}%</strong></div>
        <div>VR Mode: <strong>{gameStats.isVRActive ? '🥽 Active' : '💻 Desktop'}</strong></div>
        
        <button 
          onClick={resetStats}
          style={{
            marginTop: '10px',
            padding: '5px 10px',
            backgroundColor: '#cc6600',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '12px',
          }}
        >
          Reset Stats
        </button>
      </div>

      {/* Instructions */}
      <div style={{
        position: 'absolute',
        top: '20px',
        right: '20px',
        zIndex: 1000,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        padding: '15px',
        borderRadius: '8px',
        fontFamily: 'Arial, sans-serif',
        fontSize: '12px',
        maxWidth: '250px',
      }}>
        <h4 style={{ margin: '0 0 10px 0', color: '#00ff88' }}>
          How to Play
        </h4>
        <ul style={{ margin: 0, paddingLeft: '15px' }}>
          <li>Click "Enter VR Game" to start</li>
          <li>Use VR controller trigger to shoot</li>
          <li>Aim at the floating targets</li>
          <li>Score points by hitting targets</li>
          <li>Targets respawn after being hit</li>
        </ul>
      </div>

      {/* WebVR Game Component */}
      <WebVRGame 
        config={gameConfig}
        style={{ 
          width: '100%', 
          height: '100%',
          display: 'block',
        }}
      />
    </div>
  );
}

// Alternative minimal example
export function MinimalExample() {
  return (
    <div style={{ width: '100vw', height: '100vh' }}>
      <WebVRGame />
    </div>
  );
}

// Example with custom assets
export function CustomAssetsExample() {
  const customConfig: WebVRGameConfig = {
    assets: {
      spaceStation: '/custom-models/my-environment.glb',
      blaster: '/custom-models/my-gun.glb',
      target: '/custom-models/my-target.glb',
      font: '/custom-fonts/my-font.ttf',
      sounds: {
        laser: '/custom-sounds/my-laser.ogg',
        score: '/custom-sounds/my-score.ogg',
      },
    },
    game: {
      targetCount: 6,
      scorePerHit: 50,
    },
    visual: {
      backgroundColor: 0x330066,
      environment: 'forest',
    },
  };

  return (
    <div style={{ width: '100vw', height: '100vh' }}>
      <WebVRGame config={customConfig} />
    </div>
  );
}
