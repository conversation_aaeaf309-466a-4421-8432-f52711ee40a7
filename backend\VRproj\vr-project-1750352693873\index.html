<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>VR Space Shooter Game</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }

        #gameContainer {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        #ui {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            font-size: 18px;
            z-index: 1000;
            background: rgba(0,0,0,0.7);
            padding: 15px;
            border-radius: 10px;
        }

        #vrButton {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 15px 30px;
            color: white;
            font-size: 18px;
            border-radius: 10px;
            cursor: pointer;
            z-index: 1000;
            transition: transform 0.2s;
        }

        #vrButton:hover {
            transform: translateX(-50%) scale(1.05);
        }

        #instructions {
            position: absolute;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            text-align: center;
            z-index: 1000;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
        }

        .crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid white;
            border-radius: 50%;
            z-index: 1000;
            pointer-events: none;
        }

        .crosshair::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 4px;
            height: 4px;
            background: white;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <div id="ui">
            <div>Score: <span id="score">0</span></div>
            <div>Targets Hit: <span id="targets">0</span></div>
            <div>Health: <span id="health">100</span></div>
        </div>

        <div class="crosshair"></div>

        <div id="instructions">
            Click to shoot • WASD to move • Mouse to look around
        </div>

        <button id="vrButton">Enter VR Mode</button>
    </div>

    <script>
        // Game variables
        let scene, camera, renderer, controls;
        let targets = [];
        let bullets = [];
        let stars = [];
        let score = 0;
        let targetsHit = 0;
        let health = 100;
        let gameRunning = true;

        // Initialize the game
        function init() {
            // Create scene
            scene = new THREE.Scene();
            scene.fog = new THREE.Fog(0x000000, 1, 1000);

            // Create camera
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 0, 5);

            // Create renderer
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setClearColor(0x000011);
            document.getElementById('gameContainer').appendChild(renderer.domElement);

            // Add controls
            controls = new THREE.OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;

            // Create starfield
            createStarfield();

            // Create initial targets
            createTargets();

            // Add event listeners
            document.addEventListener('click', onMouseClick);
            document.addEventListener('keydown', onKeyDown);
            window.addEventListener('resize', onWindowResize);

            // Start game loop
            animate();
        }

        function createStarfield() {
            const starGeometry = new THREE.BufferGeometry();
            const starCount = 1000;
            const positions = new Float32Array(starCount * 3);

            for (let i = 0; i < starCount * 3; i += 3) {
                positions[i] = (Math.random() - 0.5) * 2000;
                positions[i + 1] = (Math.random() - 0.5) * 2000;
                positions[i + 2] = (Math.random() - 0.5) * 2000;
            }

            starGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));

            const starMaterial = new THREE.PointsMaterial({
                color: 0xffffff,
                size: 2,
                sizeAttenuation: false
            });

            const starField = new THREE.Points(starGeometry, starMaterial);
            scene.add(starField);
        }

        function createTargets() {
            for (let i = 0; i < 10; i++) {
                const geometry = new THREE.SphereGeometry(0.5, 8, 6);
                const material = new THREE.MeshBasicMaterial({
                    color: Math.random() * 0xffffff,
                    wireframe: true
                });
                const target = new THREE.Mesh(geometry, material);

                target.position.set(
                    (Math.random() - 0.5) * 50,
                    (Math.random() - 0.5) * 50,
                    (Math.random() - 0.5) * 50 - 20
                );

                target.userData = {
                    velocity: new THREE.Vector3(
                        (Math.random() - 0.5) * 0.1,
                        (Math.random() - 0.5) * 0.1,
                        (Math.random() - 0.5) * 0.1
                    ),
                    originalColor: target.material.color.getHex()
                };

                scene.add(target);
                targets.push(target);
            }
        }

        function onMouseClick(event) {
            if (!gameRunning) return;

            // Create bullet
            const bulletGeometry = new THREE.SphereGeometry(0.1, 4, 4);
            const bulletMaterial = new THREE.MeshBasicMaterial({ color: 0xffff00 });
            const bullet = new THREE.Mesh(bulletGeometry, bulletMaterial);

            bullet.position.copy(camera.position);

            const direction = new THREE.Vector3();
            camera.getWorldDirection(direction);
            bullet.userData = { velocity: direction.multiplyScalar(2) };

            scene.add(bullet);
            bullets.push(bullet);

            // Check for hits
            checkCollisions();
        }

        function checkCollisions() {
            bullets.forEach((bullet, bulletIndex) => {
                targets.forEach((target, targetIndex) => {
                    if (bullet.position.distanceTo(target.position) < 1) {
                        // Hit!
                        scene.remove(bullet);
                        scene.remove(target);
                        bullets.splice(bulletIndex, 1);
                        targets.splice(targetIndex, 1);

                        // Update score
                        score += 100;
                        targetsHit++;
                        updateUI();

                        // Create new target
                        setTimeout(() => {
                            if (gameRunning) {
                                createSingleTarget();
                            }
                        }, 1000);
                    }
                });
            });
        }

        function createSingleTarget() {
            const geometry = new THREE.SphereGeometry(0.5, 8, 6);
            const material = new THREE.MeshBasicMaterial({
                color: Math.random() * 0xffffff,
                wireframe: true
            });
            const target = new THREE.Mesh(geometry, material);

            target.position.set(
                (Math.random() - 0.5) * 50,
                (Math.random() - 0.5) * 50,
                (Math.random() - 0.5) * 50 - 20
            );

            target.userData = {
                velocity: new THREE.Vector3(
                    (Math.random() - 0.5) * 0.1,
                    (Math.random() - 0.5) * 0.1,
                    (Math.random() - 0.5) * 0.1
                ),
                originalColor: target.material.color.getHex()
            };

            scene.add(target);
            targets.push(target);
        }

        function onKeyDown(event) {
            const speed = 0.5;
            switch(event.code) {
                case 'KeyW':
                    camera.position.z -= speed;
                    break;
                case 'KeyS':
                    camera.position.z += speed;
                    break;
                case 'KeyA':
                    camera.position.x -= speed;
                    break;
                case 'KeyD':
                    camera.position.x += speed;
                    break;
                case 'Space':
                    event.preventDefault();
                    onMouseClick();
                    break;
            }
        }

        function updateUI() {
            document.getElementById('score').textContent = score;
            document.getElementById('targets').textContent = targetsHit;
            document.getElementById('health').textContent = health;
        }

        function animate() {
            if (!gameRunning) return;

            requestAnimationFrame(animate);

            // Update targets
            targets.forEach(target => {
                target.position.add(target.userData.velocity);
                target.rotation.x += 0.01;
                target.rotation.y += 0.01;

                // Bounce off boundaries
                if (Math.abs(target.position.x) > 25) target.userData.velocity.x *= -1;
                if (Math.abs(target.position.y) > 25) target.userData.velocity.y *= -1;
                if (Math.abs(target.position.z) > 25) target.userData.velocity.z *= -1;
            });

            // Update bullets
            bullets.forEach((bullet, index) => {
                bullet.position.add(bullet.userData.velocity);

                // Remove bullets that are too far
                if (bullet.position.length() > 100) {
                    scene.remove(bullet);
                    bullets.splice(index, 1);
                }
            });

            controls.update();
            renderer.render(scene, camera);
        }

        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        // VR Button functionality
        document.getElementById('vrButton').addEventListener('click', () => {
            if (navigator.xr) {
                navigator.xr.isSessionSupported('immersive-vr').then((supported) => {
                    if (supported) {
                        alert('🎉 VR is supported! Full WebXR implementation would start here.');
                    } else {
                        alert('❌ VR not supported on this device.');
                    }
                });
            } else {
                alert('❌ WebXR not available. Use a modern browser with WebXR support.');
            }
        });

        // Start the game
        init();
    </script>
</body>
</html>
