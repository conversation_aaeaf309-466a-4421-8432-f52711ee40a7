<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>VR Space Shooter Game</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }

        #root {
            width: 100vw;
            height: 100vh;
        }

        .loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 1000;
        }

        .error {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #ff6b6b;
            font-size: 16px;
            text-align: center;
            z-index: 1000;
            max-width: 80%;
        }

        .vr-demo {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            text-align: center;
        }

        .vr-button {
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 15px 30px;
            color: white;
            font-size: 18px;
            border-radius: 10px;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.2s;
        }

        .vr-button:hover {
            transform: scale(1.05);
        }

        .stars {
            position: absolute;
            width: 100%;
            height: 100%;
            background: radial-gradient(2px 2px at 20px 30px, #eee, transparent),
                        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
                        radial-gradient(1px 1px at 90px 40px, #fff, transparent),
                        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
                        radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
            background-repeat: repeat;
            background-size: 200px 100px;
            animation: twinkle 3s infinite;
        }

        @keyframes twinkle {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="stars"></div>
        <div class="vr-demo">
            <h1>🚀 VR Space Shooter Experience</h1>
            <p>Welcome to the immersive VR space adventure!</p>
            <p>This VR experience features:</p>
            <ul style="text-align: left; max-width: 400px;">
                <li>🎯 Interactive shooting mechanics</li>
                <li>🌌 Immersive space environment</li>
                <li>🎮 VR controller support</li>
                <li>⭐ Dynamic scoring system</li>
                <li>🔊 Spatial audio effects</li>
            </ul>
            <button class="vr-button" onclick="enterVR()">Enter VR Mode</button>
            <button class="vr-button" onclick="playDemo()">Play Demo</button>
            <p style="margin-top: 30px; font-size: 14px; opacity: 0.7;">
                Note: VR mode requires a compatible VR headset and WebXR support
            </p>
        </div>
    </div>

    <script>
        function enterVR() {
            if (navigator.xr) {
                navigator.xr.isSessionSupported('immersive-vr').then((supported) => {
                    if (supported) {
                        alert('🎉 VR is supported! The full VR experience would launch here with proper WebXR implementation.');
                    } else {
                        alert('❌ VR not supported on this device. Please use a VR-compatible browser and headset.');
                    }
                });
            } else {
                alert('❌ WebXR not available. Please use a modern browser with WebXR support.');
            }
        }

        function playDemo() {
            const root = document.getElementById('root');
            root.innerHTML = `
                <div class="stars"></div>
                <div class="vr-demo">
                    <h1>🎮 VR Demo Mode</h1>
                    <p>Simulating VR space shooter experience...</p>
                    <div style="margin: 20px 0;">
                        <div style="font-size: 24px; margin: 10px 0;">Score: <span id="score">0</span></div>
                        <div style="font-size: 18px; margin: 10px 0;">Targets Hit: <span id="targets">0</span></div>
                    </div>
                    <button class="vr-button" onclick="shoot()">🔫 Shoot Target</button>
                    <button class="vr-button" onclick="location.reload()">🔄 Restart</button>
                    <div style="margin-top: 20px; font-size: 14px; opacity: 0.7;">
                        Click "Shoot Target" to simulate the VR shooting mechanics!
                    </div>
                </div>
            `;

            // Add some demo functionality
            window.score = 0;
            window.targets = 0;

            window.shoot = function() {
                window.targets++;
                window.score += Math.floor(Math.random() * 100) + 50;
                document.getElementById('score').textContent = window.score;
                document.getElementById('targets').textContent = window.targets;

                // Add some visual feedback
                const button = event.target;
                button.style.background = 'linear-gradient(45deg, #ff6b6b 0%, #ee5a24 100%)';
                setTimeout(() => {
                    button.style.background = 'linear-gradient(45deg, #667eea 0%, #764ba2 100%)';
                }, 200);
            };
        }
    </script>
</body>
</html>
