<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>VR Space Shooter Game</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }

        #root {
            width: 100vw;
            height: 100vh;
        }

        .loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 1000;
        }

        .error {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #ff6b6b;
            font-size: 16px;
            text-align: center;
            z-index: 1000;
            max-width: 80%;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="loading">Loading VR Experience...</div>
    </div>

    <script type="module">
        // Import the compiled VR game components
        import { WebVRGame, GlobalProvider } from './index.js';
        import React from 'react';
        import { createRoot } from 'react-dom/client';

        // Initialize the React app
        const container = document.getElementById('root');
        const root = createRoot(container);

        // Render the VR game wrapped in the global provider
        root.render(
            React.createElement(GlobalProvider, null,
                React.createElement(WebVRGame)
            )
        );
    </script>
</body>
</html>
